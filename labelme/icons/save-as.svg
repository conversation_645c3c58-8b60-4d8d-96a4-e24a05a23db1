<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<!-- Created with Sodipodi ("http://www.sodipodi.com/") -->
<svg
   width="48pt"
   height="48pt"
   viewBox="0 0 48 48"
   style="overflow:visible;enable-background:new 0 0 48 48"
   xml:space="preserve"
   id="svg589"
   sodipodi:version="0.32"
   sodipodi:docname="/home/<USER>/Desktop/action/filesaveas.svg"
   sodipodi:docbase="/home/<USER>/Desktop/action"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xap="http://ns.adobe.com/xap/1.0/"
   xmlns:xapGImg="http://ns.adobe.com/xap/1.0/g/img/"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:xml="http://www.w3.org/XML/1998/namespace"
   xmlns:xapMM="http://ns.adobe.com/xap/1.0/mm/"
   xmlns:pdf="http://ns.adobe.com/pdf/1.3/"
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:a="http://ns.adobe.com/AdobeSVGViewerExtensions/3.0/"
   xmlns:x="adobe:ns:meta/"
   xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs
     id="defs677">
    <defs
       id="defs796" />
    <sodipodi:namedview
       id="namedview726" />
    <metadata
       id="metadata711">
      <sfw>
        <slices>
          <slice
             x="0"
             y="0"
             width="256"
             height="256"
             sliceID="124333141" />
        </slices>
        <sliceSourceBounds
           x="0"
           y="0"
           width="256"
           height="256"
           bottomLeftOrigin="true" />
        <optimizationSettings>
          <targetSettings
             fileFormat="PNG24Format"
             targetSettingsID="0">
            <PNG24Format
               transparency="true"
               includeCaption="false"
               interlaced="false"
               noMatteColor="false"
               matteColor="#FFFFFF"
               filtered="false" />
          </targetSettings>
        </optimizationSettings>
      </sfw>
      <xpacket>
begin='﻿' id='W5M0MpCehiHzreSzNTczkc9d'</xpacket>
      <x:xmpmeta
         x:xmptk="XMP toolkit 3.0-29, framework 1.6">
        <rdf:RDF>
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c">
            <pdf:Producer>
Adobe PDF library 5.00</pdf:Producer>
          </rdf:Description>
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c" />
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c" />
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c">
            <xap:CreateDate>
2004-01-26T11:58:28+02:00</xap:CreateDate>
            <xap:ModifyDate>
2004-03-28T20:41:40Z</xap:ModifyDate>
            <xap:CreatorTool>
Adobe Illustrator 10.0</xap:CreatorTool>
            <xap:MetadataDate>
2004-02-16T23:58:32+01:00</xap:MetadataDate>
            <xap:Thumbnails>
              <rdf:Alt>
                <rdf:li
                   rdf:parseType="Resource">
                  <xapGImg:format>
JPEG</xapGImg:format>
                  <xapGImg:width>
256</xapGImg:width>
                  <xapGImg:height>
256</xapGImg:height>
                  <xapGImg:image>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</xapGImg:image>
                </rdf:li>
              </rdf:Alt>
            </xap:Thumbnails>
          </rdf:Description>
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c">
            <xapMM:DocumentID>
uuid:4ee3f24b-6ed2-4a2e-8f7a-50b762c8da8b</xapMM:DocumentID>
          </rdf:Description>
          <rdf:Description
             rdf:about="uuid:cbee75c6-82d1-45ba-8274-b89c6084675c">
            <dc:format>
image/svg+xml</dc:format>
            <dc:title>
              <rdf:Alt>
                <rdf:li
                   xml:lang="x-default">
mime.ai</rdf:li>
              </rdf:Alt>
            </dc:title>
          </rdf:Description>
        </rdf:RDF>
      </x:xmpmeta>
      <xpacket>
end='w'</xpacket>
    </metadata>
    <linearGradient
       id="XMLID_9_"
       gradientUnits="userSpaceOnUse"
       x1="128.9995"
       y1="11"
       x2="128.9995"
       y2="245.0005">
      <stop
         offset="0"
         style="stop-color:#494949"
         id="stop717" />
      <stop
         offset="1"
         style="stop-color:#000000"
         id="stop718" />
      <a:midPointStop
         offset="0"
         style="stop-color:#494949"
         id="midPointStop719" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#494949"
         id="midPointStop720" />
      <a:midPointStop
         offset="1"
         style="stop-color:#000000"
         id="midPointStop721" />
    </linearGradient>
    <linearGradient
       id="XMLID_10_"
       gradientUnits="userSpaceOnUse"
       x1="29.0532"
       y1="29.0532"
       x2="226.9471"
       y2="226.9471">
      <stop
         offset="0"
         style="stop-color:#FFFFFF"
         id="stop725" />
      <stop
         offset="1"
         style="stop-color:#DADADA"
         id="stop726" />
      <a:midPointStop
         offset="0"
         style="stop-color:#FFFFFF"
         id="midPointStop727" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFFFFF"
         id="midPointStop728" />
      <a:midPointStop
         offset="1"
         style="stop-color:#DADADA"
         id="midPointStop729" />
    </linearGradient>
    <linearGradient
       id="XMLID_11_"
       gradientUnits="userSpaceOnUse"
       x1="-481.7007"
       y1="-94.4194"
       x2="-360.2456"
       y2="-164.2214"
       gradientTransform="matrix(0.1991 0.98 -0.98 0.1991 91.6944 573.5653)">
      <stop
         offset="0"
         style="stop-color:#990000"
         id="stop736" />
      <stop
         offset="1"
         style="stop-color:#7C0000"
         id="stop737" />
      <a:midPointStop
         offset="0"
         style="stop-color:#990000"
         id="midPointStop738" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#990000"
         id="midPointStop739" />
      <a:midPointStop
         offset="1"
         style="stop-color:#7C0000"
         id="midPointStop740" />
    </linearGradient>
    <linearGradient
       id="XMLID_12_"
       gradientUnits="userSpaceOnUse"
       x1="-1375.9844"
       y1="685.3809"
       x2="-1355.0455"
       y2="706.3217"
       gradientTransform="matrix(-0.999 0.0435 0.0435 0.999 -1277.0056 -496.5172)">
      <stop
         offset="0"
         style="stop-color:#F8F1DC"
         id="stop743" />
      <stop
         offset="1"
         style="stop-color:#D6A84A"
         id="stop744" />
      <a:midPointStop
         offset="0"
         style="stop-color:#F8F1DC"
         id="midPointStop745" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#F8F1DC"
         id="midPointStop746" />
      <a:midPointStop
         offset="1"
         style="stop-color:#D6A84A"
         id="midPointStop747" />
    </linearGradient>
    <linearGradient
       id="XMLID_13_"
       gradientUnits="userSpaceOnUse"
       x1="65.0947"
       y1="-0.7954"
       x2="137.6021"
       y2="160.1823">
      <stop
         offset="0"
         style="stop-color:#FFA700"
         id="stop750" />
      <stop
         offset="0.7753"
         style="stop-color:#FFD700"
         id="stop751" />
      <stop
         offset="1"
         style="stop-color:#FF794B"
         id="stop752" />
      <a:midPointStop
         offset="0"
         style="stop-color:#FFA700"
         id="midPointStop753" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFA700"
         id="midPointStop754" />
      <a:midPointStop
         offset="0.7753"
         style="stop-color:#FFD700"
         id="midPointStop755" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFD700"
         id="midPointStop756" />
      <a:midPointStop
         offset="1"
         style="stop-color:#FF794B"
         id="midPointStop757" />
    </linearGradient>
    <linearGradient
       id="XMLID_14_"
       gradientUnits="userSpaceOnUse"
       x1="-1336.4497"
       y1="635.7949"
       x2="-1325.3219"
       y2="622.5333"
       gradientTransform="matrix(-0.999 0.0435 0.0435 0.999 -1277.0056 -496.5172)">
      <stop
         offset="0"
         style="stop-color:#FFC957"
         id="stop763" />
      <stop
         offset="1"
         style="stop-color:#FF6D00"
         id="stop764" />
      <a:midPointStop
         offset="0"
         style="stop-color:#FFC957"
         id="midPointStop765" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFC957"
         id="midPointStop766" />
      <a:midPointStop
         offset="1"
         style="stop-color:#FF6D00"
         id="midPointStop767" />
    </linearGradient>
    <linearGradient
       id="XMLID_15_"
       gradientUnits="userSpaceOnUse"
       x1="-1401.459"
       y1="595.6309"
       x2="-1354.6851"
       y2="699.4763"
       gradientTransform="matrix(-0.999 0.0435 0.0435 0.999 -1277.0056 -496.5172)">
      <stop
         offset="0"
         style="stop-color:#FFA700"
         id="stop770" />
      <stop
         offset="0.7753"
         style="stop-color:#FFD700"
         id="stop771" />
      <stop
         offset="1"
         style="stop-color:#FF9200"
         id="stop772" />
      <a:midPointStop
         offset="0"
         style="stop-color:#FFA700"
         id="midPointStop773" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFA700"
         id="midPointStop774" />
      <a:midPointStop
         offset="0.7753"
         style="stop-color:#FFD700"
         id="midPointStop775" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFD700"
         id="midPointStop776" />
      <a:midPointStop
         offset="1"
         style="stop-color:#FF9200"
         id="midPointStop777" />
    </linearGradient>
    <linearGradient
       id="XMLID_16_"
       gradientUnits="userSpaceOnUse"
       x1="67.8452"
       y1="115.5361"
       x2="144.5898"
       y2="115.5361">
      <stop
         offset="0"
         style="stop-color:#7D7D99"
         id="stop780" />
      <stop
         offset="0.1798"
         style="stop-color:#B1B1C5"
         id="stop781" />
      <stop
         offset="0.3727"
         style="stop-color:#BCBCC8"
         id="stop782" />
      <stop
         offset="0.6825"
         style="stop-color:#C8C8CB"
         id="stop783" />
      <stop
         offset="1"
         style="stop-color:#CCCCCC"
         id="stop784" />
      <a:midPointStop
         offset="0"
         style="stop-color:#7D7D99"
         id="midPointStop785" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#7D7D99"
         id="midPointStop786" />
      <a:midPointStop
         offset="0.1798"
         style="stop-color:#B1B1C5"
         id="midPointStop787" />
      <a:midPointStop
         offset="0.2881"
         style="stop-color:#B1B1C5"
         id="midPointStop788" />
      <a:midPointStop
         offset="1"
         style="stop-color:#CCCCCC"
         id="midPointStop789" />
    </linearGradient>
  </defs>
  <sodipodi:namedview
     id="base" />
  <metadata
     id="metadata590">
    <xpacket>
begin='﻿' id='W5M0MpCehiHzreSzNTczkc9d'</xpacket>
    <x:xmpmeta
       x:xmptk="XMP toolkit 3.0-29, framework 1.6">
      <rdf:RDF>
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998">
          <pdf:Producer>
Adobe PDF library 5.00</pdf:Producer>
        </rdf:Description>
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998" />
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998" />
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998">
          <xap:CreateDate>
2004-02-04T02:08:51+02:00</xap:CreateDate>
          <xap:ModifyDate>
2004-03-29T09:20:16Z</xap:ModifyDate>
          <xap:CreatorTool>
Adobe Illustrator 10.0</xap:CreatorTool>
          <xap:MetadataDate>
2004-02-29T14:54:28+01:00</xap:MetadataDate>
          <xap:Thumbnails>
            <rdf:Alt>
              <rdf:li
                 rdf:parseType="Resource">
                <xapGImg:format>
JPEG</xapGImg:format>
                <xapGImg:width>
256</xapGImg:width>
                <xapGImg:height>
256</xapGImg:height>
                <xapGImg:image>
/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA
AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK
DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f
Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAAEAAwER
AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA
AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB
UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE
1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ
qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy
obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp
0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo
+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXYq7
FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7F
XYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FX
Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY
q7FXzd+b/wDzlWum3k+h+QxFc3EJMdzrkoEkKuNiLZPsyU/nb4fAEb50vZ/YXEBPLsP5v62meXue
A3v5mfmprl080vmLVriXdjHBcTIi17rFCVRfoXOghocEBQhH5NJmepUf8Tfmj/1dtb/6SLv/AJqy
f5fD/Nj8gjxPN3+JvzR/6u2t/wDSRd/81Y/l8P8ANj8gviebv8Tfmj/1dtb/AOki7/5qx/L4f5sf
kF8Tzd/ib80f+rtrf/SRd/8ANWP5fD/Nj8gviebv8Tfmj/1dtb/6SLv/AJqx/L4f5sfkF8Tzd/ib
80f+rtrf/SRd/wDNWP5fD/Nj8gviebv8Tfmj/wBXbW/+ki7/AOasfy+H+bH5BfE83f4m/NH/AKu2
t/8ASRd/81Y/l8P82PyC+J5u/wATfmj/ANXbW/8ApIu/+asfy+H+bH5BfE83f4m/NH/q7a3/ANJF
3/zVj+Xw/wA2PyC+J5u/xN+aP/V21v8A6SLv/mrH8vh/mx+QXxPN3+JvzR/6u2t/9JF3/wA1Y/l8
P82PyC+J5u/xN+aP/V21v/pIu/8AmrH8vh/mx+QXxPN3+JvzR/6u2t/9JF3/AM1Y/l8P82PyC+J5
u/xN+aP/AFdtb/6SLv8A5qx/L4f5sfkF8Tzd/ib80f8Aq7a3/wBJF3/zVj+Xw/zY/IL4nm7/ABN+
aP8A1dtb/wCki7/5qx/L4f5sfkF8Tzd/ib80f+rtrf8A0kXf/NWP5fD/ADY/IL4nm7/E35o/9XbW
/wDpIu/+asfy+H+bH5BfE82j5t/M+Aes2ta3EI/i9U3N2vGnfly2x/LYT/DH5BePzZ15C/5yh/Mb
y7cxRaxcHzDpQIEsF2f9IC9zHc058v8AX5D9ea/VdiYcg9I4JeXL5NkchD688jeefLvnby/DrmhT
+rayEpLE4CywygAtFKtTxYV+RG4qDnH6nTTwT4JjdyIytkGY6XYq7FXYq7FXYq7FXjX/ADlH+YV1
5W8hppunymHU/MMj2qSqaMltGoNwynxPNE/2WbrsPSDLl4pfTDf49GvJKg+VPy+8lP5ivecqM9rG
4jWFaqZpTvw57cVUULGvcfMdtYFk7Ac3Ua3VHGAI/XLk+jNK/LfSLS0SK4JYqDSGCkUCV3PBVAPX
vtXwzWT7TlfoAA+11f5Xi3mTIo608meV/wBL2lnLbSSLcc/92sB8Kk70IOU5+0s4xSmCPT5NuDRY
pZBEjmyu2/KnydcFgliF4ip5TT/wY5ov5f1f877B+p2/8kaf+b9pVv8AlT3lL/lkT/kdcf1w/wAv
az+d9kf1I/kjTfzftLR/J/yl/wAsif8AI65/rj/L2s/nfZH9S/yRpv5v2lafyg8p/wDLKn/I65/r
h/l3Wfzvsj+pf5J03837S0fyh8p/8sqf8jrn+uP8u6z+d9kf1L/JOm/m/aWj+UXlP/llj/5HXP8A
XH+XdZ/O+yP6l/knTfzftLX/ACqPyn/yzR/8jrn+uH+XNb/O+yP6l/knTd32lr/lUflX/lmj/wCR
1z/XB/Lmt/nfZH9S/wAk6bu+0u/5VD5W/wCWaP8A5HXP9cf5d1n877I/qX+SdN/N+0u/5VB5Y/5Z
ov8Akdc/1x/l3Wfzvsj+pf5J03837S7/AJU/5a/5Zov+R1z/AFx/l3Wfzvsj+pf5J03837S7/lT3
lv8A5Zov+R1z/XB/L2s/nfZH9S/yRpv5v2l3/KnfLv8AyzRf8jrn+uP8vaz+d9kf1L/JGm/m/aXf
8qc8v/8ALNF/yOuf64/y9rP532R/Uv8AJGm/m/aXf8qb0H/lmh/5HXP9cf5f1n877I/qX+SNN/N+
0u/5U1oP/LND/wAjrn+uD+X9Z/O+wfqT/JGn/m/aVk/5P6BDBJM1rEVjUswE1xWg8KnH/RBq/wCd
9g/Uv8kaf+b9pYp5i8oeXLOGBoLQo0j8SRJIe3+Uxza9ldq6jNKQnLkO4Ov1/Z2HGAYj7SkreXdK
IoEZD/Mrmo+Vaj8M3I1eR1fgRee/mD+W8NxE91ZIPrhq0UygL6rbt6ctNubfssevy6XwmJjbYjo5
ml1csUhGRuB+xJP+cfvzGvfJvny1T1T+iNXdLTUbcn4SWNIpPZkduvgTmq7Z0gy4Sf4obj9L0WOV
F93xSJLGsiGqOAyn2O+cK5K7FXYq7FXYq7FXYq+R/wDnM65lbzjoFsT+6i05pEG/2pJ2VvbpGM6/
2cH7uR/pfocfNzb/ACCs7caXZzBAJPQuJS3fn9ZMXL/gNs2uvkRirvl+h0GffUm+kfx972EnNKyU
LXfzNpZ/4y/8QOOo/wAWn8PvbdN/fRei6SPjl/1R+vOWDvyjyMsQsIwoWkYVWEYULSMKFhGSVrFV
wOBVwOBVwOBK4HFVwOBK4HAq4HAlcDgVQ1I/7jrn/jE36siUh5X5uH+j23tL/DN52F9U/c6vtX6Q
x0nOidEgNZodNmBAP2aE9jzG4+jL9P8AWGrL9JfNGuSmDzPqEsICGK9maNRsF4ykgCnhmRKArhel
08iccT5B+iHk+4afQbcsalBx+8Bv+Ns8wdknWKuxV2KuxV2KuxV8hf8AOZn/ACneif8AbLH/AFES
52Hs7/dS/rfoDj5uaO/IUf7gbI/8ulx/1GnNlr/7v/O/Q6DN/jEv6v6nqxOahksshXzJpv8Az0/4
gcjqf8Xn8PvbdL/exei6SPjk/wBUfrzlw9AmBGTYrSMKrCMKFpGFVhGFC0jChYRklaxVcDgVcDgV
cDgSuBxVcDgSuBwKuBwJUdRP+4+5/wCMTfqyJSHlvmwf6Lb+0n8M3XYX1S9zq+1fpDwzzXoX1nzD
eT8a82U1/wBgBm1y6fikS6qGfhFJt5T076lomoJSnOSM/dTMzQYuCTj6rJxh4h5k/wCUi1T/AJjJ
/wDk62bM83fab+6j/VH3P0N8jf8AHBj+Y/5NpnlztGQYq7FXYq7FXYq7FXyF/wA5mf8AKd6J/wBs
sf8AURLnYezv91L+t+gOPm5ph+Q4/wCddsj/AMutx/1Gtmx1/wBH+d+h0Gb/ABiX9X9T1InNUl2n
b+Y9P/56f8QOQ1X+Lz+H3t+l/vYvRtJH7yT/AFR+vOWDv0xIySFhGSQtIwqsIwoWkYVWEYULSMKF
hGSVrFVwOBVwOBVwOBK4HFVwOBK4HAqjf/8AHPuf+MTfqyEkh5j5rH+iQ/65/Uc3XYf1y9zre1Pp
DDpbGzkcu8QZ26k50weeMQoXVvDDZyrEgQNQkD5jLMX1BhMbPmrzN/ykmrf8xlx/ydbMp6XTf3cf
6o+5+hnkb/jgx/Mf8m0zy52bIMVdirsVdirsVdir5C/5zM/5TvRP+2WP+oiXOw9nf7qX9b9AcfNz
TL8iR/zrFif+Xa4/6jWzYa76f879Doc/9/L3fqenE5rEL9KFfMNh85P+IHK9X/cT+H3uRpP72L0f
SR+8k/1f45yzv0xIwqtIwoWEZJC0jCqwjChaRhVYRhQtIwoWEZJWsVXA4FXA4FXA4ErgcVXA4EqV
9/vBc/8AGJv1ZCXJIea+ah/ocfsx/wCInNx2H9cvcHW9qfQGIE507z6HvN7dx8v1jLMfNhPk+Z/N
H/KTav8A8xtx/wAnWzJek0/93H+qPufoX5G/44MfzH/JtM8vdmyDFXYq7FXYq7FXYq+Qv+czP+U7
0T/tlj/qIlzsPZ3+6l/W/QHHzc0z/Isf86nYH/l3uP8AqNbM/W8v879Doc/9/L3fqelk5rkK2j76
/ZfN/wDiBynWf3Evx1cjSf3oej6UP3r/AOr/ABzl3fpliq0jCq0jChYRkkLSMKrCMKFpGFVhGFC0
jChYRklaxVcDgVcDgVcDgSuBxVTvP94rn/jE36shPkyDzjzUP9BX5n/iJzbdifXL4Ou7U+gfFhhO
dS86pXG8TD5frycebGXJ8z+av+Un1j/mNuf+TrZkh6TT/wB3H+qPufoV5G/44MfzH/JtM8vdmyDF
XYq7FXYq7FXYq+Qv+czP+U70T/tlj/qIlzsPZ3+6l/W/QHHzc01/I0f86fp5/wCKLj/qNbM7W8v8
79Dos/8AfH3fqejE5gMEVoe+u2fzf/iByjW/3Evx1cnR/wB4Ho+l/wB4/wAv45y7v0xxV2KrSMKr
SMKFhGSQtIwqsIwoWkYVWEYULSMKFhGSVrFVwOBVwOBVwOBKy6P+h3H/ABib9WQnySHnnmkf6APY
t/xE5texPrPwdf2n9A+LByc6t5xTfcEZIIL5p82f8pTrP/Mdc/8AJ5syRyek0/8Adx9w+5+hPkb/
AI4MfzH/ACbTPL3ZsgxV2KuxV2KuxV2KvkL/AJzM/wCU70T/ALZY/wCoiXOw9nf7qX9b9AcfNzTf
8jx/zpWnH/im4/6jHzO1n6f0Oi1H98fd+p6ETmE1o3y/vrdr82/4gcxtd/cycrR/3gej6b/eP8v4
5y7v0wxV2KuxVaRhVaRhQsIySFpGFVhGFC0jCqwjChaRhQsIyStYquBwKuBwKtuT/olx/wAYm/Vk
J8mUXn/mkf7jj/sv+InNp2L/AHh+Dr+0/oHxYGTnWvONDdgMUPmnzb/yletf8x9z/wAnmzIjyelw
f3cfcH6EeRv+ODH8x/ybTPMHZMgxV2KuxV2KuxV2KvkL/nMz/lO9E/7ZY/6iJc7D2d/upf1v0Bx8
3NOPyRH/ADo2mn/im4/6jHzN1fP4/odHqP70+5n5OYjUmHlzfWrb5t/xA5ia7+5k5Wi/vA9H07+8
f5fxzmHfo/FXYq7FXYqtIwqtIwoWEZJC0jCqwjChaRhVYRhQtIwoWEZJWsVXA4Fan/3luP8AjE36
shk5MosD80D/AHGt8m/4gc2XY394fg4Haf0fN56TnXvNLod5VHz/AFYJclD5p83/APKWa3/zH3X/
ACebMiPIPS4P7uPuD9CPI3/HBj+Y/wCTaZ5g7JkGKuxV2KuxV2KuxV8hf85mf8p3on/bLH/URLnY
ezv91L+t+gOPm5p1+SYp5B0w/wDFVx/1GPmZq/q+P6HR6n+9PuZ0TmM0pr5Y31iD5t/xA5h6/wDu
i5mi/vA9G0/7b/LOYd8jsVdirsVdirsVWkYVWkYULCMkhaRhVYRhQtIwqsIwoWkYULCMkrWKul/3
mn/4xt+rK8nJMebB/NA/3Fyf6r/8QObHsb+8Pw+9we0/o+bzgnOxeZVLXe4QfP8AUcjPkmPN81ec
f+Uu1z/toXX/ACebL4fSHpcH0R9wfoP5G/44MfzH/JtM8xdkyDFXYq7FXYq7FXYq+Qv+czP+U70T
/tlj/qIlzsPZ3+6l/W/QHHzc08/JUf8AIPNLP/Fdx/1GSZl6r6z7/wBDpNT/AHh9zNicocdOPKu+
rQ/M/wDEGzB7Q/ui5uh+sPRbEhXappt3zmXfI3mn8w+/FXeon8w+/FWvUj/mH3jFXepH/MPvGKu9
WP8AnH3jFXepF/Ov3jFVpeP+dfvGG1Wl4/51+8YbQtLJ/Mv3jDa0tJT+ZfvGHiCKWnj/ADL/AMEP
64eILS08f5l/4If1w8QRS0qP5l/4If1w8YWlpUfzL/wS/wBceMIorCn+Uv8AwS/1w8YXhKyai289
WXeNgPiB3I+eRnIEJiGFeZx/uKm/1H/4gc2PY/8AefL73B7S+j5vNCc7N5dWsN7uMfP/AIichl+k
so83zX5z/wCUw13/ALaF1/yffL8f0j3PS4foj7g/QbyN/wAcGP5j/k2meYuyZBirsVdirsVdirsV
fIX/ADmZ/wAp3on/AGyx/wBREudh7O/3Uv636A4+bmnv5Lj/AJBxpZ/yLj/qMkzK1X1n3/odJqv7
w+5mZOVOOmvly5jtrwTyAlIzuFpXdSO9Mw9bjM4cI6uVpJiMrLK/8T2H++5fuX/mrNL/ACdk7x+P
g7b85DuLX+JbD/fcv3L/AM1Y/wAnZO8fj4L+ch3Fr/Elj/vuX7l/5qx/k7J3j8fBfzkO4tf4jsf9
9y/cv/NWP8nZO8fj4L+ch3Fo+YrH/fcv3L/zVj/J2TvH4+C/nIdxW/4hsv5JPuX/AJqx/k7J3j8f
BfzkO4tfp+y/kk+5f+asf5Oyd4/HwX85DuLX6es/5JPuX/mrH+TsnePx8F/OQ7i1+nbP+ST7l/5q
x/k7J3j8fBfzkO4tfpy0/kk+5f64/wAnZO8fj4L+ch3Fr9N2n8kn3L/XH+TsnePx8F/OQ7i0datf
5JPuX+uP8nZO8fj4L+ch3Fb+mLX+R/uH9cf5Oyd4/HwX85DuLX6Xtv5H+4f1x/k7J3j8fBfzkO4t
fpa2/lf7h/XH+TsnePx8F/OQ7i0dVt/5X+4f1x/k7J3j8fBfzkO4tHVLf+V/uH9cf5Oyd4/HwX85
DuKW6/dxz6XcKgYFY5DvT+Q++bDs7TSx5Bdbkfe4etzicNvN5sTnWPOojTN7+If63/ETleb6Cyhz
fNnnX/lMte/7aN3/AMn3y/H9I9z02H6B7g/QXyN/xwY/mP8Ak2meYuxZBirsVdirsVdirsVfIX/O
Zn/Kd6J/2yx/1ES52Hs7/dS/rfoDj5uaf/kyP+QZ6Uf8m4/6jJMytT/eH8dHS6r6z7mXk5W4rSyy
JXgxWvWhIxMQVEiOTjdXH+/X/wCCOPAO5eM9603Vz/v1/wDgjh4I9y8Z71pu7n/fz/8ABHDwR7kc
Z71pu7r/AH8//BH+uHw49y8cu9aby6/39J/wR/rh8OPcEccu9ab27/3/ACf8E39cPhx7gjjl3rTe
3f8Av+T/AINv64fDj3BfEl3rTfXn+/5P+Db+uHw49wR4ku8rTfXv/LRJ/wAG39cPhR7gviS7ytN/
e/8ALRJ/wbf1w+FHuCPEl3ladQvv+WiX/g2/rh8KPcEeJLvK06hff8tMv/Bt/XD4Ue4L4ku8rTqN
/wD8tMv/AAbf1w+FDuCPEl3ladRv/wDlpl/4Nv64fBh3D5L4ku8rTqWof8tUv/Bt/XD4MO4fJHiy
7ytOp6h/y1Tf8jG/rh8GHcPkjxZd5aOp6j/y1Tf8jG/rh8GHcPkviy7ypvqN+6lWuZWVhRlLsQQe
xFcIwwHQfJByS7yhScta0Xo++pQj/W/4icq1H0Fnj+p82+d/+Uz1/wD7aN3/AMn3y7F9I9z02H6B
7g/QTyN/xwY/mP8Ak2meZOxZBirsVdirsVdirsVfIX/OZn/Kd6J/2yx/1ES52Hs7/dS/rfoDj5ub
IfybH/ILtJPtcf8AUZLmTqP70/jo6XVfWWVE5FxFpOFVpOFDCLz82fLtrdz2slteGSCRonKpFQlC
VNKyDbbLRjLLgKgfzh8tf8s17/wEX/VXD4ZXwytP5weWv+Wa9/4CL/qrjwFHhlo/m95b/wCWa8/4
CL/qrh4Cvhlo/m75b/5Zrz/gIv8Aqrh4V8Mrf+Vt+XD/AMe15/wEX/VXCIFHhF3/ACtjy6f+Pa8/
4CL/AKqZMYijwy1/ytXy8f8Aj3u/+Ai/6qZYNPJHhl3/ACtPy+f+Pe7/AOAj/wCqmTGll5I8Mtf8
rQ0A/wDHvd/8BH/1UywaKfkjwy7/AJWboR/497r/AICP/qpkx2fPvCOAtf8AKytDP+6Lr/gI/wDq
pkx2bk7x+PgjgLY/MXRT0guf+Bj/AOa8P8nZO8fj4LwFseftIPSG4/4FP+a8f5Pn3j8fBHAUTY+b
dOvbqO2iimWSQkKXVQNhXejHwyGTSSiLNIMSE4JzGYLCcKFpOFCN0PfVYB/rf8QOU6n+7LZi+oPm
7zx/ymvmD/tpXn/J98uxfQPcHpsX0D3B+gfkb/jgx/Mf8m0zzJ2LIMVdirsVdirsVdir5C/5zM/5
TvRP+2WP+oiXOw9nf7qX9b9AcfNzZF+To/5BVpB9rj/qMlzI1H98fx0dNq/qLJycXDWk4ULScKEq
/IbT7OTVvMty0S/Wm1BoRPQcxHVmKqT0BPXNL25M3EdKd52bEUS9s/RNv/O/3j+maC3Zu/RNv/O/
3j+mNq79E2/87/eP6Y2rv0Tb/wA7/eP6Y2rv0Tb/AM7/AHj+mNq79E2/87/eP6Y2rv0Tb/zv94/p
jau/RNv/ADv94/pjau/RNv8Azv8AeP6Y2rv0Tb/zv94/pjau/RNv/O/3j+mNq80/PXTbMeUJmaMP
LbyQvBKwBZC8gRqEU6qc6L2YyyjqwAdpA38nA7RiDiJ7nzykeekEvOpz5cSmsWx9z/xE5jak+gsZ
cmeE5qWhaThQtJwqj/L2+sW4/wBf/iDZRq/7s/jq2YfqD5v89f8AKb+Yf+2nef8AUQ+W4foHuD02
L6R7n6BeRv8Ajgx/Mf8AJtM8zdiyDFXYq7FXYq7FXYq+Qv8AnMz/AJTvRP8Atlj/AKiJc7D2d/up
f1v0Bx83Nkn5Pj/kEujn/mI/6jJcvz/35/HR02r+osjJyThLScKFhOSQgvyCamo+YR46o3/G2aHt
z6o+533Zv0l7pmhdk7FXYq7FXYq7FXYq7FXYq7FXYq8w/PPfytdr7wf8nRm/9m/8bj7pfc4PaP8A
cn4PntI89IJebTXQUpqlufc/8ROY+c+gsZcmZk5rWhaThVaThQmPlrfW7Yf6/wDybbMfWf3R/HVt
wfWHzh58/wCU58xf9tO8/wCoh8twfRH3B6fH9I9z9AfI3/HBj+Y/5NpnmbsGQYq7FXYq7FXYq7FX
yF/zmZ/yneif9ssf9REudh7O/wB1L+t+gOPm5sm/KEf8gh0Y+9x/1GTZdm/vz+OgdPrOZT8nLHAW
E5JC0nCqX/kO9NT8wf8AbUb/AI2zQ9ufVH3O+7N+kvdPUzQ07Jg/5n+a7ny3o9zq0CGY20cREHMx
hvUnEfUA9OVemZmh03jZRC6u/utpz5eCBl3PIv8AoY3V/wDq1j/pKf8A5ozoR7NxP8f2ftdf/KR/
m/ay/wDLf81dQ826lcW0tsbQWypJyWZpOXJuNKELmu7U7JGliJCXFZ7nJ0ur8UkVVPZvUzR05rvU
xpXepjSu9TGld6mNK71MaV3qY0rzP8625eXrlf8AjB/ydGb32c/xuPul9zg9o/3J+DwdI89FJebT
PRkpqEJ9z+o5RmPpLCXJlJOYLStJwoWE4UJp5V31+1H/ABk/5NtmNrf7o/D727T/AFh84efv+U68
x/8AbUvf+oh8swf3cfcHp8f0j3P0B8jf8cGP5j/k2meaOwZBirsVdirsVdirsVfIX/OZn/Kd6J/2
yx/1ES52Hs7/AHUv636A4+bmyf8AKMf8gc0U/wCVcf8AUZNl2b/GD+OgdPrOZTsnLnXrScKrScKE
s/I1qanr3/bTb/jbND22PVH3O/7N+kvb/UzROyeYfny9fJmoj/iu2/6i0zbdiD/CofH/AHJcTW/3
R+H3vmQDPQ4wefep/kEeOuah/wAYov8Ak5nOe1Eaxw/rH7nZdmfUfc+l/UziXcu9TFXepirvUxV3
qYq71MVd6mKvOPzhblolwPaH/k5m79nv8aj7j9zgdo/3J+DxdI89BJebTDTEpeRH3P6jlOQ7MZck
/JzFaFhOFC0nCqbeUd/MVoP+Mn/Jpsxdf/cy+H3hu031h84/mB/ynnmT/tqXv/UQ+Waf+7j/AFR9
z0+P6R7n6AeRv+ODH8x/ybTPNHYMgxV2KuxV2KuxV2KvkL/nMz/lO9E/7ZY/6iJc7D2d/upf1v0B
x83NlP5TD/kC+iH/AC7n/qMmy3L/AIzL8dA6jWcym5OZDrlpOFC0nChKfyUbjqmue+pN/wAbZpO3
h6of1Xf9m/SXtXqZz9Oyeafnm9fKOoD/AIrt/wDqKXNz2CP8Lh/nf7kuJrv7o/D73zaFz0mMHnre
nfkWeOt33/GKP/k5nMe1kaxQ/rH7nZ9l/Ufc+j/UzhKdy71MaV3qY0rvUxpXepjSu9TGld6mNK8/
/NduWlzL7Rf8nM3XYH+NR+P3OD2l/cn4PJEjzvSXmkbYpS4Q/wCfTKpnZjLkmpOUtC0nCq0nJITj
ybv5lsx/xk/5NPmH2h/cy+H3hv0394Hzl+YP/KfeZf8Atq3v/US+Waf+7j/VH3PTw+kPv/yN/wAc
GP5j/k2meaOwZBirsVdirsVdirsVfIX/ADmZ/wAp3on/AGyx/wBREudh7O/3Uv636A4+bmyv8qB/
yBPRD/xZc/8AUZNlmT/GpfjoHUa1MycynWrScKFhOFUn/JxuOqa1/wBtJv8AjbNR7QD1Q/qu+7M+
kvZfUznKdm83/Ox+XlW/H/Fdv/1Erm69nh/hkP8AO/3JcTXf3J+H3vncLnp8YvOPSvyUHDWL0+Mc
f/E85P2u/uof1j9ztOy/qPufQ3qZwVO6d6mNK71MaV3qY0rvUxpXepjSu9TGlYJ+ZjcrGUe0X/E8
3HYX+Mx+P3OB2l/cn4PNEjzuSXmkVbpSRTlZLGXJFk5FpWk5JC0nChOvJG/miyH/ABl/5MvmF2l/
cS+H3hyNL/eD8dHzn+Yf/Kf+Zv8AtrX3/US+T0391H+qPueoh9Iff3kb/jgx/Mf8m0zzVz2QYq7F
XYq7FXYq7FXyF/zmZ/yneif9ssf9REudh7O/3Uv636A4+bmyz8qv/JHaGf8Aiy5/6jJ8nk/xuXu/
QHUa1MCczHWLCcKrScKEk/KN+Gqaz/20W/42zV+0Y3x/1Xfdl/SXr31gZzVO0Yv520E+YLSSwbms
EyIHkjKhgUk9Tbl8hmXodXLTZRliATG+fmKas2IZImJ6sFH5J2Q/3ddffF/TOh/0W5/5kPt/W4P8
lw7ynvlX8v18vXbz25mkMoVX9QpQBWrtxAzV9pdsZNXERkAOHutyNPpI4iSDzei/WBmnpy3fWBjS
u+sDGld9YGNK76wMaV31gY0rvrAxpWGfmA4kt5B/kx/8Tzbdi/4wPj9zgdpf3J+DAkjztCXmldEp
vkbYy5Licm0LScKFhOFU98ib+a7H/nr/AMmXzB7T/wAXl8PvDkaT+8H46PnT8xf/ACYPmf8A7a19
/wBRL5PTf3Uf6o+56iHIPv3yN/xwY/mP+TaZ5q57IMVdirsVdirsVdir5C/5zMB/x1oh7fosf9RE
udh7O/3Uv636A4+bmyz8qv8AyRuh07S3Ffb/AEyfJz/xuXu/QHUa3kjSczXWLScKFpOFDH/ywfhq
OsH/AJf2/W2a72lG+P8AqO+7L+kvT/rXvnMU7R31r3xpXfWvfGld9a98aV31r3xpXfWvfGld9a98
aV31r3xpXfWvfGld9a98aV31r3xpWM+bpPUiYeyf8Szadj/4wPj9zg9pf3J+DFUjzsCXmVVkpGTg
id2MuSHJy9oWE4VWk4UJ95CqfNljQbD1a/8AIl8wO1P8Xl8PvDkaP+8H46PnX8xf/Jg+Z/8AtrX3
/US+T0v91H+qPuephyD798jf8cGP5j/k2meaueyDFXYq7FXYq7FXYq+b/wDnMvyrcXGj6F5ngQtH
YSSWV6QK8VuOLxMfBQ8bLXxYZ0vs7nAlLGeu4+DTmHVif/OOXm+xvdGvfImoTiO5LvdaSXbZlIDS
RINt0ZfUp1ILeGbPtDGYTGUfF12pxcQZ/fafeWUhjuIytDQPT4W+Ry3FljMWC6acDHmhCcta1hOF
Uo/KW39fzBf2/X1dQYU/4LNf7UHfH/Ud92V9Je4/4U/yPwzkuN2tO/wp/kfhjxrTv8Kf5H4Y8a07
/Cn+R+GPGtO/wp/kfhjxrTv8Kf5H4Y8a07/Cn+R+GPGtO/wp/kfhjxrTv8Kf5H4Y8a07/Cn+R+GP
GtO/wp/kfhjxrTz78wrH6lf/AFelKxI1Pmx/pm27GN5x8fucDtP+5PwYmkedcS8wuuEpbufb+OMD
6mMuSWE5ltK0nChyJJK4jjUu7bKqgkk+wGJIAsqBfJldi1p5F0G982+Yf3BjjMdlZsQsskjbqig/
tvxoB2FSds0Wu1H5iQxY9+8u20OlINl82eV7HUPNvny1WWs1zqF4bm8cDqC5lmb2rvT3zK1mUYMB
PdGh9wd/AWafoD5TtzBo6L2LEj5ABf8AjXPPHLTjFXYq7FXYq7FXYql/mDQdL8waLeaLqsIuNPv4
mhuIj3Vu4PZlO6nsd8sxZZY5CUeYQRb4V/NL8oPNv5a656pEs2kiX1NL1uDko+FqpzZf7qVdtvHd
Sc7vQ9o49TGuUusfxzDjTgQmOjf85K/mRp1klrMbLUymy3F5C5loBQAtDJCG+ZFfE4z7KxSN7j3O
OcUSj/8Aoaf8wf8Aq36T/wAibn/soyH8kYu+X2fqR4Ad/wBDT/mD/wBW/Sf+RNz/ANlGP8kYu+X2
fqXwAoN/zkl5puryK6v9OtRJACIHsXmtXUk9SzvcfgBlObsSEuUiPfv+puxejkjP+hnPMn++bz/u
JS/9U8xv9Dw/n/7H9rd4rv8AoZzzJ/vm8/7iUv8A1Tx/0PD+f/sf2r4rv+hnPMn++bz/ALiUv/VP
H/Q8P5/+x/aviu/6Gc8yf75vP+4lL/1Tx/0PD+f/ALH9q+K7/oZzzJ/vm8/7iUv/AFTx/wBDw/n/
AOx/aviu/wChnPMn++bz/uJS/wDVPH/Q8P5/+x/aviu/6Gc8yf75vP8AuJS/9U8f9Dw/n/7H9q+K
7/oZzzJ/vm8/7iUv/VPH/Q8P5/8Asf2r4rv+hnPMn++bz/uJS/8AVPH/AEPD+f8A7H9q+K7/AKGc
8yf75vP+4lL/ANU8f9Dw/n/7H9q+K7/oZzzJ/vm8/wC4lL/1Tx/0PD+f/sf2r4qEm/5yR8yi8jvr
awikvEBQyahNLdjgRSg4mBh1/mPyy7D2FCJ3kT7hX62vJLjFK3/Q0/5g/wDVv0n/AJE3P/ZRmT/J
GLvl9n6nH8AO/wChp/zB/wCrfpP/ACJuf+yjH+SMXfL7P1L4Ad/0NP8AmD/1b9J/5E3P/ZRj/JGL
vl9n6l8AO/6Gn/MH/q36T/yJuf8Asox/kjF3y+z9S+AGj/zlP+YJH/HP0ke/o3P/AGUY/wAkYu+X
2fqXwQwPXvM/nfz/AKxF9emm1O7qRa2cS0jiDHf040AVR0qx32+I5lxhi08L2iO9tjCtg+ifyJ/J
ubQF+u36q+tXajmRusEXXiD+vxNPAE8f2r2l+YlUfoH2+f6nKhCn0XBCkEKQxiiRgKv0ZqGxfirs
VdirsVdirsVdiqhfWFlf2slpewpcW0o4yQyKGVh7g4QSNwryzXP+cZ/yy1G4a4i0xIGY1McTyQrX
5RMo/wCFzYY+1tTAUJn40fvYHGEp/wChVPy+/wCWAf8ASXdf1yf8tar+f9kf1L4cXf8AQqn5ff8A
LAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/
rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n
/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF
3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff
8sA/6S7r+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r
+uP8tar+f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+
f9kf1L4cXf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4c
Xf8AQqn5ff8ALAP+ku6/rj/LWq/n/ZH9S+HF3/Qqn5ff8sA/6S7r+uP8tar+f9kf1L4cW1/5xW/L
9WDCwWo33urkj7icT2zqv5/2R/UvhxZl5Z/KLy9oKcLG1t7RduRgT42p4sQN/c5g5tRkym5yMmQA
DNrOytrSL04E4j9o9ST7nKUq+KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K
uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku
xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux
V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV//2Q==</xapGImg:image>
              </rdf:li>
            </rdf:Alt>
          </xap:Thumbnails>
        </rdf:Description>
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998">
          <xapMM:DocumentID>
uuid:f3c53255-be8a-4b04-817b-695bf2c54c8b</xapMM:DocumentID>
        </rdf:Description>
        <rdf:Description
           rdf:about="uuid:9dfcc10e-f4e2-4cbf-91b0-8deea2f1a998">
          <dc:format>
image/svg+xml</dc:format>
          <dc:title>
            <rdf:Alt>
              <rdf:li
                 xml:lang="x-default">
filesave.ai</rdf:li>
            </rdf:Alt>
          </dc:title>
        </rdf:Description>
      </rdf:RDF>
    </x:xmpmeta>
    <xpacket>
end='w'</xpacket>
  </metadata>
  <g
     id="Layer_1">
    <path
       style="opacity:0.2;"
       d="M9.416,5.208c-2.047,0-3.712,1.693-3.712,3.775V39.15c0,2.082,1.666,3.775,3.712,3.775h29.401     c2.047,0,3.712-1.693,3.712-3.775V8.983c0-2.082-1.665-3.775-3.712-3.775H9.416z"
       id="path592" />
    <path
       style="opacity:0.2;"
       d="M9.041,4.833c-2.047,0-3.712,1.693-3.712,3.775v30.167c0,2.082,1.666,3.775,3.712,3.775h29.401     c2.047,0,3.712-1.693,3.712-3.775V8.608c0-2.082-1.665-3.775-3.712-3.775H9.041z"
       id="path593" />
    <path
       style="fill:#00008D;"
       d="M8.854,4.646c-2.047,0-3.712,1.693-3.712,3.775v30.167c0,2.082,1.666,3.775,3.712,3.775h29.401     c2.047,0,3.712-1.693,3.712-3.775V8.42c0-2.082-1.665-3.775-3.712-3.775H8.854z"
       id="path594" />
    <path
       style="fill:#00008D;"
       d="M8.854,5.021c-1.84,0-3.337,1.525-3.337,3.4v30.167c0,1.875,1.497,3.4,3.337,3.4h29.401     c1.84,0,3.337-1.525,3.337-3.4V8.42c0-1.875-1.497-3.4-3.337-3.4H8.854z"
       id="path595" />
    <path
       id="path166_1_"
       style="fill:#FFFFFF;"
       d="M40.654,38.588c0,1.36-1.074,2.463-2.399,2.463H8.854c-1.326,0-2.4-1.103-2.4-2.463V8.42     c0-1.36,1.074-2.462,2.4-2.462h29.401c1.325,0,2.399,1.103,2.399,2.462V38.588z" />
    <linearGradient
       id="path166_2_"
       gradientUnits="userSpaceOnUse"
       x1="-149.0464"
       y1="251.1436"
       x2="-149.0464"
       y2="436.303"
       gradientTransform="matrix(0.1875 0 0 -0.1875 51.5 83.75)">
      <stop
         offset="0"
         style="stop-color:#B4E2FF"
         id="stop598" />
      <stop
         offset="1"
         style="stop-color:#006DFF"
         id="stop599" />
      <a:midPointStop
         offset="0"
         style="stop-color:#B4E2FF"
         id="midPointStop600" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#B4E2FF"
         id="midPointStop601" />
      <a:midPointStop
         offset="1"
         style="stop-color:#006DFF"
         id="midPointStop602" />
    </linearGradient>
    <path
       id="path166"
       style="fill:url(#path166_2_);"
       d="M40.654,38.588c0,1.36-1.074,2.463-2.399,2.463H8.854c-1.326,0-2.4-1.103-2.4-2.463V8.42     c0-1.36,1.074-2.462,2.4-2.462h29.401c1.325,0,2.399,1.103,2.399,2.462V38.588z" />
    <path
       style="fill:#FFFFFF;"
       d="M8.854,6.521c-1.013,0-1.837,0.852-1.837,1.9v30.167c0,1.048,0.824,1.9,1.837,1.9h29.401     c1.013,0,1.837-0.853,1.837-1.9V8.42c0-1.048-0.824-1.9-1.837-1.9H8.854z"
       id="path604" />
    <linearGradient
       id="XMLID_1_"
       gradientUnits="userSpaceOnUse"
       x1="7.3057"
       y1="7.2559"
       x2="50.7728"
       y2="50.7231">
      <stop
         offset="0"
         style="stop-color:#94CAFF"
         id="stop606" />
      <stop
         offset="1"
         style="stop-color:#006DFF"
         id="stop607" />
      <a:midPointStop
         offset="0"
         style="stop-color:#94CAFF"
         id="midPointStop608" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#94CAFF"
         id="midPointStop609" />
      <a:midPointStop
         offset="1"
         style="stop-color:#006DFF"
         id="midPointStop610" />
    </linearGradient>
    <path
       style="fill:url(#XMLID_1_);"
       d="M8.854,6.521c-1.013,0-1.837,0.852-1.837,1.9v30.167c0,1.048,0.824,1.9,1.837,1.9h29.401     c1.013,0,1.837-0.853,1.837-1.9V8.42c0-1.048-0.824-1.9-1.837-1.9H8.854z"
       id="path611" />
    <linearGradient
       id="XMLID_2_"
       gradientUnits="userSpaceOnUse"
       x1="23.5039"
       y1="2.187"
       x2="23.5039"
       y2="34.4368">
      <stop
         offset="0"
         style="stop-color:#428AFF"
         id="stop613" />
      <stop
         offset="1"
         style="stop-color:#C9E6FF"
         id="stop614" />
      <a:midPointStop
         offset="0"
         style="stop-color:#428AFF"
         id="midPointStop615" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#428AFF"
         id="midPointStop616" />
      <a:midPointStop
         offset="1"
         style="stop-color:#C9E6FF"
         id="midPointStop617" />
    </linearGradient>
    <path
       style="fill:url(#XMLID_2_);"
       d="M36.626,6.861c0,0-26.184,0-26.914,0c0,0.704,0,16.59,0,17.294c0.721,0,26.864,0,27.583,0     c0-0.704,0-16.59,0-17.294C36.988,6.861,36.626,6.861,36.626,6.861z"
       id="path618" />
    <polygon
       id="path186_1_"
       style="fill:#FFFFFF;"
       points="35.809,6.486 10.221,6.486 10.221,23.405 36.788,23.405 36.788,6.486 " />
    <linearGradient
       id="path186_2_"
       gradientUnits="userSpaceOnUse"
       x1="-104.5933"
       y1="411.6699"
       x2="-206.815"
       y2="309.4482"
       gradientTransform="matrix(0.1875 0 0 -0.1875 51.5 83.75)">
      <stop
         offset="0"
         style="stop-color:#CCCCCC"
         id="stop621" />
      <stop
         offset="1"
         style="stop-color:#F0F0F0"
         id="stop622" />
      <a:midPointStop
         offset="0"
         style="stop-color:#CCCCCC"
         id="midPointStop623" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#CCCCCC"
         id="midPointStop624" />
      <a:midPointStop
         offset="1"
         style="stop-color:#F0F0F0"
         id="midPointStop625" />
    </linearGradient>
    <polygon
       id="path186"
       style="fill:url(#path186_2_);"
       points="35.809,6.486 10.221,6.486 10.221,23.405 36.788,23.405 36.788,6.486 " />
    <path
       style="fill:#FFFFFF;stroke:#FFFFFF;stroke-width:0.1875;"
       d="M11.488,7.019c0,0.698,0,14.542,0,15.239c0.716,0,23.417,0,24.133,0c0-0.698,0-14.541,0-15.239     C34.904,7.019,12.204,7.019,11.488,7.019z"
       id="path627" />
    <linearGradient
       id="XMLID_3_"
       gradientUnits="userSpaceOnUse"
       x1="34.5967"
       y1="3.5967"
       x2="18.4087"
       y2="19.7847">
      <stop
         offset="0"
         style="stop-color:#FFFFFF"
         id="stop629" />
      <stop
         offset="0.5506"
         style="stop-color:#E6EDFF"
         id="stop630" />
      <stop
         offset="1"
         style="stop-color:#FFFFFF"
         id="stop631" />
      <a:midPointStop
         offset="0"
         style="stop-color:#FFFFFF"
         id="midPointStop632" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#FFFFFF"
         id="midPointStop633" />
      <a:midPointStop
         offset="0.5506"
         style="stop-color:#E6EDFF"
         id="midPointStop634" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#E6EDFF"
         id="midPointStop635" />
      <a:midPointStop
         offset="1"
         style="stop-color:#FFFFFF"
         id="midPointStop636" />
    </linearGradient>
    <path
       style="fill:url(#XMLID_3_);stroke:#FFFFFF;stroke-width:0.1875;"
       d="M11.488,7.019c0,0.698,0,14.542,0,15.239c0.716,0,23.417,0,24.133,0c0-0.698,0-14.541,0-15.239     C34.904,7.019,12.204,7.019,11.488,7.019z"
       id="path637" />
    <linearGradient
       id="path205_1_"
       gradientUnits="userSpaceOnUse"
       x1="-174.4409"
       y1="300.0908"
       x2="-108.8787"
       y2="210.2074"
       gradientTransform="matrix(0.1875 0 0 -0.1875 51.5 83.75)">
      <stop
         offset="0"
         style="stop-color:#003399"
         id="stop639" />
      <stop
         offset="0.2697"
         style="stop-color:#0035ED"
         id="stop640" />
      <stop
         offset="1"
         style="stop-color:#57ADFF"
         id="stop641" />
      <a:midPointStop
         offset="0"
         style="stop-color:#003399"
         id="midPointStop642" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#003399"
         id="midPointStop643" />
      <a:midPointStop
         offset="0.2697"
         style="stop-color:#0035ED"
         id="midPointStop644" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#0035ED"
         id="midPointStop645" />
      <a:midPointStop
         offset="1"
         style="stop-color:#57ADFF"
         id="midPointStop646" />
    </linearGradient>
    <rect
       id="path205"
       x="12.154"
       y="26.479"
       style="fill:url(#path205_1_);"
       width="22.007"
       height="13.978" />
    <linearGradient
       id="XMLID_4_"
       gradientUnits="userSpaceOnUse"
       x1="21.8687"
       y1="25.1875"
       x2="21.8687"
       y2="44.6251">
      <stop
         offset="0"
         style="stop-color:#DFDFDF"
         id="stop649" />
      <stop
         offset="1"
         style="stop-color:#7D7D99"
         id="stop650" />
      <a:midPointStop
         offset="0"
         style="stop-color:#DFDFDF"
         id="midPointStop651" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#DFDFDF"
         id="midPointStop652" />
      <a:midPointStop
         offset="1"
         style="stop-color:#7D7D99"
         id="midPointStop653" />
    </linearGradient>
    <path
       style="fill:url(#XMLID_4_);"
       d="M13.244,27.021c-0.311,0-0.563,0.252-0.563,0.563v13.104c0,0.312,0.252,0.563,0.563,0.563h17.249     c0.311,0,0.563-0.251,0.563-0.563V27.583c0-0.311-0.252-0.563-0.563-0.563H13.244z M18.85,30.697c0,0.871,0,5.078,0,5.949     c-0.683,0-2.075,0-2.759,0c0-0.871,0-5.078,0-5.949C16.775,30.697,18.167,30.697,18.85,30.697z"
       id="path654" />
    <linearGradient
       id="XMLID_5_"
       gradientUnits="userSpaceOnUse"
       x1="-158.0337"
       y1="288.0684"
       x2="-158.0337"
       y2="231.3219"
       gradientTransform="matrix(0.1875 0 0 -0.1875 51.5 83.75)">
      <stop
         offset="0"
         style="stop-color:#F0F0F0"
         id="stop656" />
      <stop
         offset="0.6348"
         style="stop-color:#CECEDB"
         id="stop657" />
      <stop
         offset="0.8595"
         style="stop-color:#B1B1C5"
         id="stop658" />
      <stop
         offset="1"
         style="stop-color:#FFFFFF"
         id="stop659" />
      <a:midPointStop
         offset="0"
         style="stop-color:#F0F0F0"
         id="midPointStop660" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#F0F0F0"
         id="midPointStop661" />
      <a:midPointStop
         offset="0.6348"
         style="stop-color:#CECEDB"
         id="midPointStop662" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#CECEDB"
         id="midPointStop663" />
      <a:midPointStop
         offset="0.8595"
         style="stop-color:#B1B1C5"
         id="midPointStop664" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#B1B1C5"
         id="midPointStop665" />
      <a:midPointStop
         offset="1"
         style="stop-color:#FFFFFF"
         id="midPointStop666" />
    </linearGradient>
    <path
       style="fill:url(#XMLID_5_);"
       d="M13.244,27.583v13.104h17.249V27.583H13.244z M19.413,37.209h-3.884v-7.074h3.884V37.209z"
       id="path667" />
    <linearGradient
       id="path228_1_"
       gradientUnits="userSpaceOnUse"
       x1="-68.1494"
       y1="388.4561"
       x2="-68.1494"
       y2="404.6693"
       gradientTransform="matrix(0.1875 0 0 -0.1875 51.5 83.75)">
      <stop
         offset="0"
         style="stop-color:#3399FF"
         id="stop669" />
      <stop
         offset="1"
         style="stop-color:#000000"
         id="stop670" />
      <a:midPointStop
         offset="0"
         style="stop-color:#3399FF"
         id="midPointStop671" />
      <a:midPointStop
         offset="0.5"
         style="stop-color:#3399FF"
         id="midPointStop672" />
      <a:midPointStop
         offset="1"
         style="stop-color:#000000"
         id="midPointStop673" />
    </linearGradient>
    <rect
       id="path228"
       x="37.83"
       y="9.031"
       style="fill:url(#path228_1_);"
       width="1.784"
       height="1.785" />
    <polyline
       id="_x3C_Slice_x3E_"
       style="fill:none;"
       points="0,48 0,0 48,0 48,48 " />
  </g>
  <rect
     id="rect810"
     fill="none"
     width="256"
     height="256"
     transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
     style="font-size:12;fill:none;" />
  <g
     id="g979"
     transform="matrix(0.207200,1.691268,-1.691268,0.207200,86.28419,53.75496)">
    <path
       opacity="0.2"
       d="M191.924,195.984c-11.613-36.127-13.717-42.67-14.859-44.064c0.119,0.076,0.289,0.178,0.289,0.178     l-78.55-87.455c-4.195-4.65-14.005,0.356-21.355,6.976c-7.283,6.542-13.32,15.773-9.37,20.564l78.944,87.543l0.533,0.094     l37.768,17.602l7.688,2.365L191.924,195.984z"
       id="path731"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;opacity:0.2;" />
    <path
       opacity="0.2"
       d="M193.557,193.516c-11.611-36.125-13.713-42.67-14.855-44.064c0.117,0.072,0.287,0.178,0.287,0.178     l-78.545-87.455c-4.199-4.651-14.015,0.355-21.361,6.975c-7.281,6.545-13.32,15.773-9.368,20.566l78.945,87.539l0.533,0.1     l37.77,17.598l7.682,2.367L193.557,193.516z"
       id="path732"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;opacity:0.2;" />
    <path
       opacity="0.2"
       d="M186.773,191.049c-11.613-36.127-13.713-42.672-14.863-44.068c0.121,0.074,0.295,0.18,0.295,0.18     L93.653,59.704c-4.192-4.65-14.009,0.359-21.354,6.978c-7.283,6.542-13.321,15.771-9.369,20.565l78.942,87.541l0.535,0.096     l37.768,17.598l7.686,2.367L186.773,191.049z"
       id="path733"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;opacity:0.2;" />
    <path
       fill="#FFFFFF"
       d="M186.43,189.355c-11.613-36.125-13.713-42.666-14.863-44.061c0.123,0.072,0.293,0.18,0.293,0.18     L93.314,58.016c-4.199-4.651-14.015,0.357-21.359,6.977c-7.283,6.543-13.322,15.774-9.37,20.566l78.941,87.541l0.535,0.098     l37.771,17.598l7.686,2.363L186.43,189.355z"
       id="path734"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:#ffffff;" />
    <path
       fill="url(#XMLID_11_)"
       d="M186.43,189.355c-11.613-36.125-13.713-42.666-14.863-44.061c0.123,0.072,0.293,0.18,0.293,0.18     L93.314,58.016c-4.199-4.651-14.015,0.357-21.359,6.977c-7.283,6.543-13.322,15.774-9.37,20.566l78.941,87.541l0.535,0.098     l37.771,17.598l7.686,2.363L186.43,189.355z"
       id="path741"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_11_);" />
    <path
       fill="url(#XMLID_12_)"
       d="M166.969,147.762l13.723,38.129l-36.371-17.902l0.168-0.152c-0.25-0.08-0.496-0.178-0.701-0.316     l-0.125,0.121l-75.303-83.57l0.123-0.104c-2.246-2.49,1.032-9.094,7.308-14.752c6.28-5.652,13.18-8.219,15.425-5.733     l75.292,83.565L166.969,147.762z"
       id="path748"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_12_);" />
    <path
       fill="url(#XMLID_13_)"
       d="M148.652,170.121c2.076-0.369,4.635-1.479,7.252-3.139c1.617-1.018,3.279-2.283,4.898-3.744     c1.455-1.303,2.736-2.666,3.84-4.01c2.076-2.531,3.322-5.213,3.781-7.424l-1.455-4.043l-0.463-0.715L91.707,64.028     c0.608,2.24-0.962,5.938-4.063,9.74c-1.134,1.389-2.441,2.789-3.945,4.141c-1.574,1.419-3.195,2.652-4.767,3.654     c-4.493,2.871-8.628,3.928-10.548,2.486l-0.025,0.021l75.303,83.57l0.125-0.121c0.205,0.139,0.451,0.236,0.701,0.316     l-0.168,0.152L148.652,170.121z"
       id="path758"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_13_);" />
    <path
       fill="#FFFFFF"
       d="M68.083,83.41c1.732,1.772,5.994,0.776,10.643-2.194c1.541-0.982,3.132-2.193,4.677-3.586     c1.476-1.325,2.759-2.701,3.872-4.063c3.578-4.388,5.091-8.642,3.477-10.584l0.023-0.024l75.817,84.119     c0.635,2.262-0.588,6.498-3.754,10.357c-1.082,1.318-2.34,2.656-3.77,3.934c-1.588,1.434-3.219,2.676-4.807,3.676     c-4.74,3.006-9.303,4.199-11.016,2.301c-0.393-0.439-2.098-2.336-2.145-2.406L67.845,83.626L68.083,83.41z"
       id="path759"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:#ffffff;" />
    <path
       fill="#FFFFFF"
       d="M75.79,69.215c6.28-5.652,13.18-8.219,15.425-5.733l16.961,18.828l1.152,26.49l-17.973,0.784     L68.359,84.071l0.123-0.104C66.236,81.477,69.514,74.874,75.79,69.215z"
       id="path760"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:#ffffff;" />
    <path
       fill="#FFFFFF"
       d="M68.083,83.41c1.732,1.772,5.994,0.776,10.643-2.194c1.541-0.982,3.132-2.193,4.677-3.586     c1.476-1.325,2.759-2.701,3.872-4.063c3.578-4.388,5.091-8.642,3.477-10.584l0.023-0.024l75.817,84.119     c0.635,2.262-0.588,6.498-3.754,10.357c-1.082,1.318-2.34,2.656-3.77,3.934c-1.588,1.434-3.219,2.676-4.807,3.676     c-4.74,3.006-9.303,4.199-11.016,2.301c-0.393-0.439-2.098-2.336-2.145-2.406L67.845,83.626L68.083,83.41z"
       id="path761"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:#ffffff;" />
    <path
       fill="url(#XMLID_14_)"
       d="M75.79,69.215c6.28-5.652,13.18-8.219,15.425-5.733l16.961,18.828l1.152,26.49l-17.973,0.784     L68.359,84.071l0.123-0.104C66.236,81.477,69.514,74.874,75.79,69.215z"
       id="path768"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_14_);" />
    <path
       fill="url(#XMLID_15_)"
       d="M68.083,83.41c1.732,1.772,5.994,0.776,10.643-2.194c1.541-0.982,3.132-2.193,4.677-3.586     c1.476-1.325,2.759-2.701,3.872-4.063c3.578-4.388,5.091-8.642,3.477-10.584l0.023-0.024l75.817,84.119     c0.635,2.262-0.588,6.498-3.754,10.357c-1.082,1.318-2.34,2.656-3.77,3.934c-1.588,1.434-3.219,2.676-4.807,3.676     c-4.74,3.006-9.303,4.199-11.016,2.301c-0.393-0.439-2.098-2.336-2.145-2.406L67.845,83.626L68.083,83.41z"
       id="path778"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_15_);" />
    <path
       fill="url(#XMLID_16_)"
       d="M74.357,90.713c0,0,6.036-0.212,10.685-3.182c1.542-0.983,3.132-2.193,4.677-3.586     c1.477-1.326,2.76-2.701,3.873-4.064c2.928-3.589,4.469-7.088,4.049-9.307l-6.865-7.617l-0.023,0.024     c1.614,1.942,0.102,6.196-3.477,10.584c-1.113,1.362-2.396,2.738-3.872,4.063c-1.545,1.393-3.136,2.604-4.677,3.586     c-4.648,2.971-8.91,3.967-10.643,2.194l-0.238,0.217l73.256,81.311c0.047,0.07,1.752,1.967,2.145,2.406     c0.342,0.377,0.799,0.627,1.344,0.771L74.357,90.713z"
       id="path790"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:url(#XMLID_16_);" />
    <path
       fill="#003333"
       d="M172.035,175.354c-1.635,1.477-3.307,2.764-4.949,3.84l13.605,6.697l-5.096-14.156     C174.537,172.953,173.352,174.176,172.035,175.354z"
       id="path791"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;fill:#003333;" />
    <path
       opacity="0.5"
       fill="#FFFFFF"
       d="M163.121,157.053L86.968,73.93c0.1-0.12,0.213-0.242,0.307-0.364     c1.428-1.752,2.52-3.49,3.225-5.058l75.768,82.707C165.715,153.039,164.668,155.082,163.121,157.053z"
       id="path792"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;opacity:0.5;fill:#ffffff;" />
    <path
       opacity="0.5"
       fill="#FFFFFF"
       d="M87.275,73.566c0.634-0.774,1.189-1.548,1.694-2.3l76.015,82.974     c-0.578,1.063-1.283,2.146-2.146,3.193c-0.744,0.896-1.566,1.805-2.465,2.697L84.152,76.932     C85.316,75.824,86.361,74.692,87.275,73.566z"
       id="path793"
       transform="matrix(0.125000,0.000000,0.000000,0.125000,-41.51768,12.75884)"
       style="font-size:12;opacity:0.5;fill:#ffffff;" />
  </g>
</svg>
