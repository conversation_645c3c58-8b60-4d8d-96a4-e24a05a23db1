#!/usr/bin/env python3
"""
Build script for creating labelme standalone executable on Windows
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """Check if required tools are installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller is installed")
    except ImportError:
        print("✗ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    try:
        import osam
        print("✓ osam is installed")
        return osam.__file__
    except ImportError:
        print("✗ osam not found. Please install labelme first: pip install labelme")
        sys.exit(1)

def clean_build_environment():
    """Clean previous build artifacts and problematic files"""
    print("Cleaning build environment...")

    # Remove previous build artifacts
    for path in ["build", "dist", "__pycache__"]:
        if os.path.exists(path):
            print(f"Removing {path}/")
            shutil.rmtree(path, ignore_errors=True)

    # Remove .spec files
    for spec_file in Path(".").glob("*.spec"):
        print(f"Removing {spec_file}")
        spec_file.unlink()

    # Check for problematic numpy directories
    current_dir = Path(".")
    for item in current_dir.iterdir():
        if item.is_dir() and item.name.lower() in ["numpy", "scipy", "matplotlib"]:
            print(f"⚠️  Warning: Found {item.name} directory in current path")
            print(f"   This might cause import conflicts. Consider moving it.")

def build_executable():
    """Build the executable using PyInstaller"""

    # Clean environment first
    clean_build_environment()

    # Get paths
    labelme_path = Path(__file__).parent / "labelme"
    osam_path = Path(check_requirements()).parent

    print(f"Labelme path: {labelme_path}")
    print(f"OSAM path: {osam_path}")

    # Ensure paths exist
    if not labelme_path.exists():
        print(f"✗ Labelme source not found at {labelme_path}")
        print("Please run this script from the labelme source directory")
        sys.exit(1)
    
    # Build command with numpy fix
    cmd = [
        "pyinstaller",
        str(labelme_path / "__main__.py"),
        "--name=Labelme",
        "--windowed",
        "--noconfirm",
        "--specpath=build",
        "--clean",  # Clean cache
        "--distpath=dist",
        "--workpath=build",
        f"--add-data={osam_path}/_models/yoloworld/clip/bpe_simple_vocab_16e6.txt.gz{os.pathsep}osam/_models/yoloworld/clip",
        f"--add-data={labelme_path}/config/default_config.yaml{os.pathsep}labelme/config",
        f"--add-data={labelme_path}/icons/*{os.pathsep}labelme/icons",
        f"--add-data={labelme_path}/translate/*{os.pathsep}translate",
        f"--icon={labelme_path}/icons/icon.png",
        "--onedir",
        # Fix numpy import issues
        "--collect-all=numpy",
        "--collect-all=numpy.core",
        "--collect-all=numpy.lib",
        "--hidden-import=numpy.core._methods",
        "--hidden-import=numpy.lib.format",
        # Additional options for better compatibility
        "--collect-all=osam",
        "--collect-all=onnxruntime",
        "--hidden-import=PyQt5.sip",
        "--hidden-import=loguru",
        "--hidden-import=imgviz",
        "--hidden-import=natsort",
        "--hidden-import=scikit-image",
        "--hidden-import=pkg_resources.py2_warn",
        # Exclude problematic modules
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib.tests",
    ]
    
    print("Building executable...")
    print("Command:", " ".join(cmd))
    
    try:
        subprocess.check_call(cmd)
        print("✓ Build completed successfully!")
        print("Executable location: dist/Labelme/")
    except subprocess.CalledProcessError as e:
        print(f"✗ Build failed with error: {e}")
        sys.exit(1)

def create_installer_script():
    """Create a simple installer script"""
    installer_content = '''@echo off
echo Installing Labelme dependencies...
pip install labelme
echo.
echo Building executable...
python build_exe.py
echo.
echo Build complete! Check the dist/Labelme/ folder.
pause
'''
    
    with open("install_and_build.bat", "w") as f:
        f.write(installer_content)
    
    print("✓ Created install_and_build.bat")

if __name__ == "__main__":
    print("Labelme Executable Builder")
    print("=" * 30)
    
    # Check if we're on Windows
    if sys.platform != "win32":
        print("Warning: This script is designed for Windows. You may need to adjust paths for other platforms.")
    
    build_executable()
    create_installer_script()
    
    print("\nNext steps:")
    print("1. Copy the dist/Labelme/ folder to your target Windows machine")
    print("2. Run Labelme.exe from that folder")
    print("3. The executable includes all dependencies and models")
