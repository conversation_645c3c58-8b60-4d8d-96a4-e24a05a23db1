#!/usr/bin/env python3
"""
Fix numpy import issues in PyInstaller builds
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def diagnose_environment():
    """Diagnose potential issues with the build environment"""
    print("🔍 Diagnosing build environment...")
    print("=" * 50)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check current directory
    current_dir = Path.cwd()
    print(f"Current directory: {current_dir}")
    
    # Check for problematic directories
    problematic_dirs = ["numpy", "scipy", "matplotlib", "pandas"]
    for dir_name in problematic_dirs:
        if (current_dir / dir_name).exists():
            print(f"⚠️  WARNING: Found {dir_name}/ directory in current path")
            print(f"   This can cause import conflicts!")
    
    # Check numpy installation
    try:
        import numpy
        print(f"✓ Numpy version: {numpy.__version__}")
        print(f"✓ Numpy location: {numpy.__file__}")
    except ImportError as e:
        print(f"✗ Numpy import error: {e}")
        return False
    
    # Check PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller not installed")
        return False
    
    return True

def fix_numpy_issues():
    """Apply fixes for common numpy issues"""
    print("\n🔧 Applying numpy fixes...")
    print("=" * 50)
    
    # 1. Upgrade numpy and PyInstaller
    print("1. Upgrading numpy and PyInstaller...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "--upgrade", "numpy", "pyinstaller"
        ])
        print("✓ Packages upgraded successfully")
    except subprocess.CalledProcessError:
        print("⚠️  Package upgrade failed, continuing...")
    
    # 2. Clear PyInstaller cache
    print("\n2. Clearing PyInstaller cache...")
    cache_dirs = [
        Path.home() / ".cache" / "pyinstaller",
        Path.cwd() / "__pycache__",
        Path.cwd() / "build",
        Path.cwd() / "dist"
    ]
    
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            print(f"Removing {cache_dir}")
            shutil.rmtree(cache_dir, ignore_errors=True)
    
    # 3. Remove .spec files
    print("\n3. Removing old .spec files...")
    for spec_file in Path.cwd().glob("*.spec"):
        print(f"Removing {spec_file}")
        spec_file.unlink()

def create_fixed_spec_file():
    """Create a .spec file with numpy fixes"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Import paths
import sys
import os
from pathlib import Path

# Get paths
labelme_path = Path("labelme")
try:
    import osam
    osam_path = Path(osam.__file__).parent
except ImportError:
    print("Error: osam not found. Please install labelme first.")
    sys.exit(1)

a = Analysis(
    [str(labelme_path / "__main__.py")],
    pathex=[],
    binaries=[],
    datas=[
        (str(osam_path / "_models/yoloworld/clip/bpe_simple_vocab_16e6.txt.gz"), "osam/_models/yoloworld/clip"),
        (str(labelme_path / "config/default_config.yaml"), "labelme/config"),
        (str(labelme_path / "icons"), "labelme/icons"),
        (str(labelme_path / "translate"), "translate"),
    ],
    hiddenimports=[
        'PyQt5.sip',
        'loguru',
        'imgviz',
        'natsort',
        'scikit-image',
        'numpy.core._methods',
        'numpy.lib.format',
        'numpy.core._dtype_ctypes',
        'pkg_resources.py2_warn',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib.tests'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Fix numpy issues
a.datas += Tree('numpy', prefix='numpy', excludes=['*.pyc', '*.pyo'])

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Labelme',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(labelme_path / "icons/icon.png"),
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Labelme',
)
'''
    
    with open("labelme_fixed.spec", "w") as f:
        f.write(spec_content)
    
    print("✓ Created labelme_fixed.spec with numpy fixes")

def build_with_spec():
    """Build using the fixed spec file"""
    print("\n🚀 Building with fixed spec file...")
    print("=" * 50)
    
    try:
        subprocess.check_call([
            "pyinstaller", 
            "--clean",
            "labelme_fixed.spec"
        ])
        print("✓ Build completed successfully!")
        print("📁 Executable location: dist/Labelme/")
    except subprocess.CalledProcessError as e:
        print(f"✗ Build failed: {e}")
        return False
    
    return True

def main():
    print("Labelme Numpy Issue Fixer")
    print("=" * 50)
    
    # Step 1: Diagnose
    if not diagnose_environment():
        print("❌ Environment check failed. Please fix the issues above.")
        return
    
    # Step 2: Apply fixes
    fix_numpy_issues()
    
    # Step 3: Create fixed spec
    create_fixed_spec_file()
    
    # Step 4: Build
    if build_with_spec():
        print("\n🎉 Success! Your labelme executable should now work.")
        print("\nTesting suggestions:")
        print("1. Navigate to dist/Labelme/")
        print("2. Run Labelme.exe")
        print("3. If it still fails, check the console output for other errors")
    else:
        print("\n❌ Build failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
