@echo off
echo ========================================
echo Labelme Windows Executable Builder
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python first: https://python.org
    pause
    exit /b 1
)

echo Step 1: Installing required packages...
pip install pyinstaller
pip install labelme

echo.
echo Step 2: Getting OSAM path...
for /f "delims=" %%i in ('python -c "import os, osam; print(os.path.dirname(osam.__file__))"') do set OSAM_PATH=%%i
set LABELME_PATH=.\labelme

echo LABELME_PATH: %LABELME_PATH%
echo OSAM_PATH: %OSAM_PATH%

echo.
echo Step 3: Building executable with PyInstaller...
pyinstaller %LABELME_PATH%\__main__.py ^
  --name=Labelme ^
  --windowed ^
  --noconfirm ^
  --specpath=build ^
  --add-data="%OSAM_PATH%\_models\yoloworld\clip\bpe_simple_vocab_16e6.txt.gz;osam\_models\yoloworld\clip" ^
  --add-data="%LABELME_PATH%\config\default_config.yaml;labelme\config" ^
  --add-data="%LABELME_PATH%\icons\*;labelme\icons" ^
  --add-data="%LABELME_PATH%\translate\*;translate" ^
  --icon="%LABELME_PATH%\icons\icon.png" ^
  --collect-all=osam ^
  --collect-all=onnxruntime ^
  --hidden-import=PyQt5.sip ^
  --hidden-import=loguru ^
  --hidden-import=imgviz ^
  --hidden-import=natsort ^
  --hidden-import=scikit-image ^
  --onedir

if errorlevel 1 (
    echo.
    echo Error: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: dist\Labelme\
echo You can now copy the entire dist\Labelme\ folder to any Windows machine
echo and run Labelme.exe without installing Python or dependencies.
echo.
pause
